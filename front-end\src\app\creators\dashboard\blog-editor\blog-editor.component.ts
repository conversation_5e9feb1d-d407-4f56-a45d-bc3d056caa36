import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';

export interface BlogBlock {
  id: string;
  type: 'heading' | 'paragraph' | 'image' | 'quote' | 'code';
  content: string;
  imageFile?: File;
  imageUrl?: string;
  order: number;
}

@Component({
  selector: 'app-blog-editor',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatCardModule,
    DragDropModule
  ],
  templateUrl: './blog-editor.component.html',
  styleUrls: ['./blog-editor.component.css']
})
export class BlogEditorComponent implements OnInit {
  @Input() initialBlocks: BlogBlock[] = [];
  @Input() readonly: boolean = false;
  @Output() blocksChange = new EventEmitter<BlogBlock[]>();

  blocks: BlogBlock[] = [];
  
  blockTypes = [
    { value: 'heading', label: 'Heading', icon: 'title' },
    { value: 'paragraph', label: 'Paragraph', icon: 'notes' },
    { value: 'image', label: 'Image', icon: 'image' },
    { value: 'quote', label: 'Quote', icon: 'format_quote' },
    { value: 'code', label: 'Code', icon: 'code' }
  ];

  ngOnInit(): void {
    if (this.initialBlocks.length > 0) {
      this.blocks = [...this.initialBlocks];
    } else {
      // Start with a default paragraph block
      this.addBlock('paragraph');
    }
  }

  addBlock(type: BlogBlock['type']): void {
    const newBlock: BlogBlock = {
      id: this.generateId(),
      type,
      content: '',
      order: this.blocks.length
    };
    
    this.blocks.push(newBlock);
    this.emitChange();
  }

  removeBlock(index: number): void {
    this.blocks.splice(index, 1);
    this.updateBlockOrders();
    this.emitChange();
  }

  onBlockContentChange(index: number, content: string): void {
    this.blocks[index].content = content;
    this.emitChange();
  }

  onImageSelected(index: number, event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.blocks[index].imageFile = file;
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        this.blocks[index].imageUrl = reader.result as string;
        this.emitChange();
      };
      reader.readAsDataURL(file);
    }
  }

  onDrop(event: CdkDragDrop<BlogBlock[]>): void {
    if (!this.readonly) {
      moveItemInArray(this.blocks, event.previousIndex, event.currentIndex);
      this.updateBlockOrders();
      this.emitChange();
    }
  }

  private updateBlockOrders(): void {
    this.blocks.forEach((block, index) => {
      block.order = index;
    });
  }

  private emitChange(): void {
    this.blocksChange.emit([...this.blocks]);
  }

  private generateId(): string {
    return 'block_' + Math.random().toString(36).substr(2, 9);
  }

  getBlockIcon(type: string): string {
    const blockType = this.blockTypes.find(bt => bt.value === type);
    return blockType ? blockType.icon : 'notes';
  }

  getBlockLabel(type: string): string {
    const blockType = this.blockTypes.find(bt => bt.value === type);
    return blockType ? blockType.label : 'Unknown';
  }
}
