<div class="calendar-view-container">
  <!-- Header Section -->
  <div class="calendar-header">
    <div class="header-controls">
      <!-- Month Navigation -->
      <div class="month-navigation">
        <button mat-icon-button (click)="navigateToPreviousMonth()" class="nav-button">
          <mat-icon>chevron_left</mat-icon>
        </button>
        
        <div class="month-display">
          <h2 class="month-title">{{ (currentDate$ | async) | date:'MMMM yyyy' }}</h2>
          <input matInput [matDatepicker]="picker" 
                 [value]="currentDate$ | async"
                 (dateChange)="onDatePickerChange($event.value)"
                 style="display: none;">
          <mat-datepicker #picker></mat-datepicker>
          <button mat-icon-button (click)="picker.open()" class="date-picker-button">
            <mat-icon>calendar_today</mat-icon>
          </button>
        </div>
        
        <button mat-icon-button (click)="navigateToNextMonth()" class="nav-button">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <button mat-stroked-button (click)="navigateToToday()" class="today-button">
          <mat-icon>today</mat-icon>
          Today
        </button>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
      <!-- Search -->
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search posts</mat-label>
        <input matInput [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()" 
               placeholder="Search by title...">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <!-- Status Filters -->
      <div class="status-filters">
        <span class="filter-label">Filter by status:</span>
        <mat-chip-listbox class="status-chips">
          <mat-chip-option 
            *ngFor="let status of statusOptions"
            [selected]="isStatusSelected(status)"
            (click)="onStatusFilterChange(status)"
            [style.background-color]="isStatusSelected(status) ? getStatusConfig(status).color : 'transparent'"
            [style.color]="isStatusSelected(status) ? 'white' : getStatusConfig(status).color"
            class="status-chip">
            <mat-icon>{{ getStatusConfig(status).icon }}</mat-icon>
            {{ getStatusConfig(status).label }}
          </mat-chip-option>
        </mat-chip-listbox>
        
        <button mat-button (click)="clearFilters()" class="clear-filters">
          <mat-icon>clear</mat-icon>
          Clear Filters
        </button>
      </div>
    </div>
  </div>

  <!-- Calendar Grid -->
  <mat-card class="calendar-card">
    <mat-card-content>
      <!-- Week Days Header -->
      <div class="calendar-grid">
        <div class="week-header">
          <div *ngFor="let day of weekDays" class="week-day-header">
            {{ day }}
          </div>
        </div>

        <!-- Calendar Days -->
        <div class="calendar-body">
          <div 
            *ngFor="let day of (calendarDays$ | async); trackBy: trackByDate"
            class="calendar-day"
            [class.other-month]="!day.isCurrentMonth"
            [class.today]="day.isToday"
            [class.selected]="day.isSelected"
            [class.has-events]="day.events.length > 0"
            (click)="onDayClick(day)">
            
            <!-- Day Number -->
            <div class="day-number">
              {{ day.date.getDate() }}
            </div>

            <!-- Events Container -->
            <div class="posts-container" *ngIf="day.events.length > 0">
              <div 
                *ngFor="let item of day.events.slice(0, 3); trackBy: trackByEventId"
                class="post-card"
                [style.border-left-color]="getStatusConfig(item.status).color"
                [matTooltip]="getPostTooltip(item)"
                matTooltipPosition="above"
                (click)="onPostClick($event, item)">
                
                <!-- Event Time (for posts) -->
                <div class="post-time" *ngIf="item.type === 'post'">{{ item.start | date:'shortTime' }}</div>
                
                <!-- Event Content -->
                <div class="post-content">
                  <!-- Thumbnail Preview (for posts) -->
                  <div class="thumbnail-preview" *ngIf="item.type === 'post' && item.mediaUrl">
                    <img
                      *ngIf="item.mediaType === 'image'"
                      [src]="item.mediaUrl"
                      [alt]="item.title"
                      class="thumbnail-image"
                      (click)="onThumbnailClick(item, $event)">
                    <div
                      *ngIf="item.mediaType === 'video'"
                      class="video-thumbnail"
                      (click)="onThumbnailClick(item, $event)">
                      <img [src]="item.mediaUrl" [alt]="item.title" class="thumbnail-image">
                      <mat-icon class="play-icon">play_circle_filled</mat-icon>
                    </div>
                    <div
                      *ngIf="item.mediaType === 'file'"
                      class="file-thumbnail"
                      (click)="onThumbnailClick(item, $event)">
                      <mat-icon class="file-icon">attach_file</mat-icon>
                    </div>
                  </div>

                  <!-- Media Icon (fallback for posts) -->
                  <div class="media-icon" *ngIf="item.type === 'post' && item.mediaType && !item.mediaUrl">
                    <mat-icon class="media-type-icon">
                      {{ item.mediaType === 'image' ? 'image' : item.mediaType === 'video' ? 'videocam' : 'attach_file' }}
                    </mat-icon>
                  </div>

                  <!-- Event Info -->
                  <div class="post-info">
                    <div class="post-title">
                      {{ getTruncatedTitle(item.title, 15) }}
                    </div>
                    <div class="blog-author" *ngIf="item.type === 'blog' && item.author_name">
                      By {{ item.author_name }}
                    </div>
                    <div class="project-info" *ngIf="item.projectTitle">
                      <span class="project-name">{{ getTruncatedTitle(item.projectTitle, 12) }}</span>
                      <span class="company-name">{{ getTruncatedTitle(item.companyName || '', 10) }}</span>
                    </div>
                  </div>
                </div>

                <!-- Status Badge -->
                <div class="status-badge" 
                     [style.background-color]="getStatusConfig(item.status).color">
                  <mat-icon class="status-icon">{{ getStatusConfig(item.status).icon }}</mat-icon>
                </div>
              </div>

              <!-- More Events Indicator -->
              <div *ngIf="day.events.length > 3" class="more-posts-indicator">
                +{{ day.events.length - 3 }} more
              </div>
            </div>

            <!-- Empty Day Indicator -->
            <div *ngIf="day.events.length === 0 && day.isCurrentMonth" class="empty-day-hint">
              <mat-icon class="add-icon">add</mat-icon>
            </div>
          </div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Floating Action Button -->
  <button mat-fab 
          class="fab-new-post" 
          color="primary"
          (click)="openCreatePostDialog()"
          matTooltip="Create New Post"
          matTooltipPosition="left">
    <mat-icon>add</mat-icon>
  </button>
</div>
