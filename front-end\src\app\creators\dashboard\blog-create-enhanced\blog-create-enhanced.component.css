.blog-create-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.main-card {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: #666;
}

.loading-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tab-content {
  padding: 24px 0;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.cover-image-section {
  margin: 24px 0;
}

.cover-image-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #333;
}

.cover-image-preview {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.cover-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Preview Section */
.preview-section {
  padding: 24px 0;
}

.preview-section h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  color: #333;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.preview-content {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
}

.preview-cover {
  margin-bottom: 24px;
  text-align: center;
}

.preview-cover-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.preview-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #333;
  line-height: 1.2;
}

.preview-meta {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f0f0f0;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.preview-meta p {
  margin: 8px 0;
  color: #666;
}

.preview-blocks {
  margin-top: 24px;
}

.preview-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-create-container {
    padding: 16px;
  }
  
  .action-buttons,
  .preview-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .action-buttons button,
  .preview-actions button {
    width: 100%;
  }
  
  .preview-title {
    font-size: 2rem;
  }
  
  .preview-content {
    padding: 16px;
  }
  
  .tab-content {
    padding: 16px 0;
  }
}

/* Material Design Enhancements */
.mat-mdc-card-header {
  background-color: #f5f5f5;
  border-radius: 8px 8px 0 0;
}

.mat-mdc-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mat-mdc-tab-group {
  margin-bottom: 24px;
}

.mat-mdc-tab-body-content {
  overflow: visible !important;
}

/* Button Styling */
.mat-mdc-raised-button {
  min-width: 120px;
}

.mat-mdc-raised-button .mat-icon {
  margin-right: 8px;
}

/* Form Field Styling */
.mat-mdc-form-field {
  margin-bottom: 16px;
}

.mat-mdc-form-field .mat-mdc-form-field-icon-suffix {
  color: #666;
}

/* Success and Error States */
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}
