<div class="blog-create-container">
  <mat-card class="main-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>{{ isEditMode ? 'edit' : 'add_circle' }}</mat-icon>
        {{ isEditMode ? 'Edit Blog' : 'Create New Blog' }}
      </mat-card-title>
      <mat-card-subtitle>
        {{ isEditMode ? 'Update your blog content' : 'Create engaging blog content with our block-based editor' }}
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-icon class="loading-icon">hourglass_empty</mat-icon>
        <p>Loading...</p>
      </div>

      <div *ngIf="!isLoading && !showPreview">
        <mat-tab-group>
          <!-- Basic Information Tab -->
          <mat-tab label="Basic Info">
            <div class="tab-content">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Blog Title</mat-label>
                <input matInput [(ngModel)]="blog.title" required placeholder="Enter blog title...">
                <mat-icon matSuffix>title</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Reference Scripture (optional)</mat-label>
                <input matInput [(ngModel)]="blog.scripture" placeholder="Enter scripture reference...">
                <mat-icon matSuffix>book</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Project</mat-label>
                <mat-select [(ngModel)]="blog.project" required>
                  <mat-option *ngFor="let project of assignedProjects" [value]="project.id">
                    {{ project.title || project.name }} ({{ project.company.name }})
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>folder</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Tags</mat-label>
                <input matInput [(ngModel)]="blog.tags" placeholder="Enter tags separated by commas...">
                <mat-icon matSuffix>label</mat-icon>
              </mat-form-field>

              <!-- Cover Image Upload -->
              <div class="cover-image-section">
                <h3>
                  <mat-icon>image</mat-icon>
                  Cover Image
                </h3>
                <input 
                  type="file" 
                  #coverImageInput
                  (change)="onCoverImageSelected($event)"
                  accept="image/*"
                  style="display: none;">
                <button 
                  mat-raised-button 
                  color="primary"
                  (click)="coverImageInput.click()">
                  <mat-icon>cloud_upload</mat-icon>
                  Choose Cover Image
                </button>
                <div *ngIf="coverImageUrl" class="cover-image-preview">
                  <img [src]="coverImageUrl" alt="Cover image preview" class="cover-image">
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Content Editor Tab -->
          <mat-tab label="Content">
            <div class="tab-content">
              <app-blog-editor
                [initialBlocks]="contentBlocks"
                [readonly]="false"
                (blocksChange)="onBlocksChange($event)">
              </app-blog-editor>
            </div>
          </mat-tab>
        </mat-tab-group>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button 
            mat-raised-button 
            (click)="cancel()"
            [disabled]="isSubmitting">
            <mat-icon>cancel</mat-icon>
            Cancel
          </button>
          
          <button 
            mat-raised-button 
            color="accent"
            (click)="onPreview()"
            [disabled]="!isFormValid() || isSubmitting">
            <mat-icon>visibility</mat-icon>
            Preview
          </button>
          
          <button 
            mat-raised-button 
            color="primary"
            (click)="onSubmit()"
            [disabled]="!isFormValid() || isSubmitting">
            <mat-icon *ngIf="!isSubmitting">{{ isEditMode ? 'save' : 'publish' }}</mat-icon>
            <mat-icon *ngIf="isSubmitting" class="spinning">hourglass_empty</mat-icon>
            {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Blog' : 'Create Blog') }}
          </button>
        </div>
      </div>

      <!-- Preview Section -->
      <div *ngIf="showPreview" class="preview-section">
        <h2>
          <mat-icon>visibility</mat-icon>
          Blog Preview
        </h2>
        
        <div class="preview-content">
          <div *ngIf="coverImageUrl" class="preview-cover">
            <img [src]="coverImageUrl" alt="Cover image" class="preview-cover-image">
          </div>
          
          <h1 class="preview-title">{{ blog.title }}</h1>
          
          <div class="preview-meta">
            <p *ngIf="blog.scripture"><strong>Scripture:</strong> {{ blog.scripture }}</p>
            <p *ngIf="blog.tags"><strong>Tags:</strong> {{ blog.tags }}</p>
          </div>
          
          <div class="preview-blocks">
            <app-blog-editor
              [initialBlocks]="contentBlocks"
              [readonly]="true">
            </app-blog-editor>
          </div>
        </div>
        
        <div class="preview-actions">
          <button
            mat-raised-button
            (click)="cancelPreview()">
            <mat-icon>edit</mat-icon>
            Back to Edit
          </button>

          <button
            mat-raised-button
            color="primary"
            (click)="onSubmit()"
            [disabled]="isSubmitting">
            <mat-icon *ngIf="!isSubmitting">{{ isEditMode ? 'save' : 'publish' }}</mat-icon>
            <mat-icon *ngIf="isSubmitting" class="spinning">hourglass_empty</mat-icon>
            {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Blog' : 'Confirm & Create') }}
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
