import {
  <PERSON>rowser<PERSON><PERSON><PERSON><PERSON><PERSON>er,
  Browser<PERSON>etTestability,
  BrowserModule,
  By,
  <PERSON><PERSON>ventsPlugin,
  <PERSON>S<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mpl,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  HydrationFeatureKind,
  KeyEventsPlugin,
  Meta,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache
} from "./chunk-LIJXTFOF.js";
import {
  DomRendererFactory2,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost
} from "./chunk-CGZQOXU2.js";
import "./chunk-6IN35RZY.js";
import {
  getDOM
} from "./chunk-HQFLPRH3.js";
import "./chunk-DHHKDAWC.js";
import "./chunk-ECCHVEJG.js";
import "./chunk-RAVBJYCC.js";
import "./chunk-RCAQEZRN.js";
import "./chunk-LGH3YRHA.js";
import "./chunk-UXQ2CSKB.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM
};
