<div class="blog-editor-container">
  <!-- Add Block Toolbar -->
  <div class="toolbar" *ngIf="!readonly">
    <h3>
      <mat-icon>edit</mat-icon>
      Blog Content Editor
    </h3>
    <div class="add-block-buttons">
      <button 
        mat-raised-button 
        color="primary" 
        *ngFor="let blockType of blockTypes"
        (click)="addBlock(blockType.value)"
        class="add-block-btn">
        <mat-icon>{{ blockType.icon }}</mat-icon>
        {{ blockType.label }}
      </button>
    </div>
  </div>

  <!-- Blocks Container -->
  <div 
    class="blocks-container"
    cdkDropList
    (cdkDropListDropped)="onDrop($event)"
    [cdkDropListDisabled]="readonly">
    
    <div 
      *ngFor="let block of blocks; let i = index"
      class="block-item"
      cdkDrag
      [cdkDragDisabled]="readonly">
      
      <mat-card class="block-card">
        <!-- Block Header -->
        <div class="block-header">
          <div class="block-info">
            <mat-icon class="block-type-icon">{{ getBlockIcon(block.type) }}</mat-icon>
            <span class="block-type-label">{{ getBlockLabel(block.type) }}</span>
          </div>
          <div class="block-actions" *ngIf="!readonly">
            <button 
              mat-icon-button 
              color="warn" 
              (click)="removeBlock(i)"
              matTooltip="Remove block">
              <mat-icon>delete</mat-icon>
            </button>
            <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
          </div>
        </div>

        <!-- Block Content -->
        <div class="block-content">
          <!-- Heading Block -->
          <mat-form-field 
            *ngIf="block.type === 'heading'" 
            appearance="outline" 
            class="full-width">
            <mat-label>Heading Text</mat-label>
            <input 
              matInput 
              [(ngModel)]="block.content"
              (ngModelChange)="onBlockContentChange(i, $event)"
              [readonly]="readonly"
              placeholder="Enter heading text...">
          </mat-form-field>

          <!-- Paragraph Block -->
          <mat-form-field 
            *ngIf="block.type === 'paragraph'" 
            appearance="outline" 
            class="full-width">
            <mat-label>Paragraph Text</mat-label>
            <textarea 
              matInput 
              rows="4"
              [(ngModel)]="block.content"
              (ngModelChange)="onBlockContentChange(i, $event)"
              [readonly]="readonly"
              placeholder="Enter paragraph text..."></textarea>
          </mat-form-field>

          <!-- Image Block -->
          <div *ngIf="block.type === 'image'" class="image-block">
            <div *ngIf="!readonly" class="image-upload">
              <input 
                type="file" 
                #imageInput
                (change)="onImageSelected(i, $event)"
                accept="image/*"
                style="display: none;">
              <button 
                mat-raised-button 
                color="primary"
                (click)="imageInput.click()">
                <mat-icon>cloud_upload</mat-icon>
                Choose Image
              </button>
            </div>
            <div *ngIf="block.imageUrl" class="image-preview">
              <img [src]="block.imageUrl" alt="Block image" class="block-image">
            </div>
            <mat-form-field 
              appearance="outline" 
              class="full-width">
              <mat-label>Image Caption (optional)</mat-label>
              <input 
                matInput 
                [(ngModel)]="block.content"
                (ngModelChange)="onBlockContentChange(i, $event)"
                [readonly]="readonly"
                placeholder="Enter image caption...">
            </mat-form-field>
          </div>

          <!-- Quote Block -->
          <mat-form-field 
            *ngIf="block.type === 'quote'" 
            appearance="outline" 
            class="full-width">
            <mat-label>Quote Text</mat-label>
            <textarea 
              matInput 
              rows="3"
              [(ngModel)]="block.content"
              (ngModelChange)="onBlockContentChange(i, $event)"
              [readonly]="readonly"
              placeholder="Enter quote text..."></textarea>
          </mat-form-field>

          <!-- Code Block -->
          <mat-form-field 
            *ngIf="block.type === 'code'" 
            appearance="outline" 
            class="full-width">
            <mat-label>Code</mat-label>
            <textarea 
              matInput 
              rows="6"
              [(ngModel)]="block.content"
              (ngModelChange)="onBlockContentChange(i, $event)"
              [readonly]="readonly"
              placeholder="Enter code..."
              class="code-textarea"></textarea>
          </mat-form-field>
        </div>
      </mat-card>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="blocks.length === 0 && !readonly" class="empty-state">
    <mat-icon class="empty-icon">article</mat-icon>
    <h3>No content blocks yet</h3>
    <p>Add your first content block using the buttons above</p>
  </div>
</div>
