import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { forkJoin } from 'rxjs';

import { CompanyAdminService } from './company-admin.service';
import { DashboardStats, ContentPost, Creator, AssignedProject, Blog } from './models/dashboard.models';
import { ContentPreviewDialogComponent } from './content-preview-dialog/content-preview-dialog.component';
import { BlogReviewDialogComponent } from './blog-review-dialog/blog-review-dialog.component';
import { PostReviewDialogComponent } from './post-review-dialog/post-review-dialog.component';

@Component({
  selector: 'app-company-admin',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatGridListModule,
    MatListModule,
    MatDividerModule,
    MatBadgeModule,
    MatTabsModule,
    MatDialogModule,
    MatExpansionModule
  ],
  templateUrl: './company-admin.component.html',
  styleUrl: './company-admin.component.css'
})
export class CompanyAdminComponent implements OnInit {
  dashboardStats: DashboardStats | null = null;
  recentContent: (ContentPost & {type: 'post'} | Blog & {type: 'blog'})[] = [];
  pendingReviews: (ContentPost & {type: 'post'} | Blog & {type: 'blog'})[] = [];
  creators: Creator[] = [];
  assignedProjects: AssignedProject[] = [];
  availableCreators: Creator[] = [];

  isLoading = false;
  currentDate = new Date();
  selectedTabIndex = 0;

  // Table columns
  pendingReviewsColumns: string[] = ['title', 'creator', 'project', 'scheduled_date', 'actions'];
  recentContentColumns: string[] = ['title', 'creator', 'status', 'scheduled_date'];
  creatorsColumns: string[] = ['name', 'projects', 'total_posts', 'pending_posts', 'status'];
  projectsColumns: string[] = ['title', 'creators', 'posts', 'pending', 'deadline', 'actions'];

  constructor(
    private router: Router,
    public companyAdminService: CompanyAdminService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // Load all dashboard data
    this.companyAdminService.getDashboardStats().subscribe({
      next: (stats) => {
        this.dashboardStats = stats;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading dashboard stats:', error);
        this.showError('Failed to load dashboard statistics');
        this.isLoading = false;
      }
    });

    this.companyAdminService.getRecentContent().subscribe({
      next: (content) => {
        this.recentContent = content.map(item => {
          if (this.isPost(item)) {
            return { ...item, type: 'post' } as ContentPost & {type: 'post'};
          } else {
            return { ...item, type: 'blog' } as Blog & {type: 'blog'};
          }
        });
      },
      error: (error) => {
        console.error('Error loading recent content:', error);
      }
    });

    // Combine pending posts and blogs for pending reviews
    forkJoin([
      this.companyAdminService.getPendingReviews(),
      this.companyAdminService.getPendingBlogs()
    ]).subscribe({
      next: ([posts, blogs]) => {
        const mappedPosts = posts.map(post => ({ ...post, type: 'post' } as ContentPost & { type: 'post' }));
        const mappedBlogs = blogs.map(blog => ({ ...blog, type: 'blog' } as Blog & { type: 'blog' }));
        this.pendingReviews = [...mappedPosts, ...mappedBlogs].sort((a, b) => {
          const dateA = new Date(this.getSubmittedDate(a));
          const dateB = new Date(this.getSubmittedDate(b));
          return dateA.getTime() - dateB.getTime();
        });
        // Debug: Log combined pending reviews
        this.pendingReviews.forEach(review => {
          if (this.isPost(review)) {
            console.log(`Post: ${review.title}, Media URL: ${review.media_url}, Is Image: ${this.isImage(review.media_url || '')}`);
          } else if (this.isBlog(review)) {
            console.log(`Blog: ${review.title}, Cover Image: ${review.cover_image}`);
          }
        });

      },
      error: (error) => {
        console.error('Error loading pending reviews:', error);
      }
    });

    this.companyAdminService.getCreators().subscribe({
      next: (creators) => {
        this.creators = creators;
      },
      error: (error) => {
        console.error('Error loading creators:', error);
      }
    });

    // Load assigned projects
    this.companyAdminService.getAssignedProjects().subscribe({
      next: (projects) => {
        this.assignedProjects = projects;
      },
      error: (error) => {
        console.error('Error loading assigned projects:', error);
      }
    });

    // Load available creators for assignment
    this.companyAdminService.getAvailableCreators().subscribe({
      next: (creators) => {
        this.availableCreators = creators;
      },
      error: (error) => {
        console.error('Error loading available creators:', error);
      }
    });
  }

  // Content Actions
  openContentPreviewDialog(content: ContentPost | Blog): void {
    let dialogData: any = {
      title: content.title,
      status: content.status,
      content: content.type === 'blog' ? content.content : content.description,
      tags: content.type === 'blog' ? content.tags || '' : '',
    };

    if (content.type === 'blog') {
      dialogData.project_name = content.project_detail?.name || content.project_detail?.title;
      dialogData.author_name = content.author_name;
      dialogData.cover_image_url = content.cover_image;
      dialogData.type = 'blog';
    } else if (content.type === 'post') {
      dialogData.project_name = content.project_name;
      dialogData.creator_name = content.creator_name;
      dialogData.media_url = content.media_url;
      dialogData.media_type = this.getMediaType(content.media_url || '');
      dialogData.file_name = this.getFileName(content.media_url || '');
      dialogData.type = 'post';
    }

    this.dialog.open(ContentPreviewDialogComponent, {
      width: '800px',
      data: dialogData
    });
  }

  approvePost(postId: number) {
    this.companyAdminService.approvePost(postId).subscribe({
      next: (response) => {
        this.showSuccess(response.message);
        this.loadDashboardData(); // Refresh data
      },
      error: (error) => {
        console.error('Error approving post:', error);
        this.showError('Failed to approve post');
      }
    });
  }

  rejectPost(postId: number) {
    this.dialog.open(PostReviewDialogComponent, {
      width: '400px',
      data: { actionType: 'reject' }
    }).afterClosed().subscribe(reviewComments => {
      if (reviewComments !== undefined) { // Check if dialog was closed with data (not cancelled)
        this.companyAdminService.rejectPost(postId, reviewComments).subscribe({
          next: (response) => {
            this.showSuccess(response.message || 'Post rejected successfully!');
            this.loadDashboardData();
          },
          error: (error) => {
            console.error('Error rejecting post:', error);
            this.showError('Failed to reject post.');
          }
        });
      }
    });
  }

  requestChangesPost(postId: number) {
    this.dialog.open(PostReviewDialogComponent, {
      width: '400px',
      data: { actionType: 'changes' }
    }).afterClosed().subscribe(reviewComments => {
      if (reviewComments !== undefined) { // Check if dialog was closed with data (not cancelled)
        this.companyAdminService.requestChangesPost(postId, reviewComments).subscribe({
          next: (response) => {
            this.showSuccess(response.message || 'Changes requested successfully!');
            this.loadDashboardData();
          },
          error: (error) => {
            console.error('Error requesting changes for post:', error);
            this.showError('Failed to request changes for post.');
          }
        });
      }
    });
  }

  approveBlog(blogId: number, comments?: string) {
    this.companyAdminService.approveBlog(blogId, comments).subscribe({
      next: (response) => {
        this.showSuccess(response.message || 'Blog approved successfully!');
        this.loadDashboardData();
      },
      error: (error) => {
        console.error('Error approving blog:', error);
        this.showError('Failed to approve blog.');
      }
    });
  }

  rejectBlog(blogId: number, reason: string) {
    this.dialog.open(BlogReviewDialogComponent, {
      width: '400px',
      data: { action: 'reject', blogId: blogId }
    }).afterClosed().subscribe(result => {
      if (result && result.action === 'reject') {
        this.companyAdminService.rejectBlog(blogId, result.comments).subscribe({
          next: (response) => {
            this.showSuccess(response.message || 'Blog rejected successfully!');
            this.loadDashboardData();
          },
          error: (error) => {
            console.error('Error rejecting blog:', error);
            this.showError('Failed to reject blog.');
          }
        });
      }
    });
  }

  requestBlogChanges(blogId: number, instructions: string) {
    this.dialog.open(BlogReviewDialogComponent, {
      width: '400px',
      data: { action: 'changes', blogId: blogId, currentComments: instructions || '' }
    }).afterClosed().subscribe(result => {
      if (result && result.action === 'changes') {
        this.companyAdminService.requestBlogChanges(blogId, result.comments).subscribe({
          next: (response) => {
            this.showSuccess(response.message || 'Changes requested successfully!');
            this.loadDashboardData();
          },
          error: (error) => {
            console.error('Error requesting blog changes:', error);
            this.showError('Failed to request blog changes.');
          }
        });
      }
    });
  }

  // Utility methods
  getStatusColor(status: string): string {
    switch (status) {
      case 'submitted': return '#ff9800'; // Orange for pending
      case 'approved': return '#4caf50'; // Green
      case 'rejected': return '#f44336'; // Red
      case 'changes_requested': return '#2196f3'; // Blue
      case 'draft': return '#9e9e9e'; // Grey
      case 'posted': return '#673ab7'; // Deep Purple
      case 'scheduled': return '#00bcd4'; // Cyan
      case 'rework': return '#ff5722'; // Deep Orange
      default: return '#9e9e9e';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'submitted': return 'schedule';
      case 'approved': return 'check_circle';
      case 'rejected': return 'cancel';
      case 'changes_requested': return 'info';
      case 'draft': return 'create';
      case 'posted': return 'done_all';
      case 'scheduled': return 'event';
      case 'rework': return 'edit_note';
      default: return 'help';
    }
  }

  formatDate(dateString: string | undefined): string {
    if (!dateString) return 'No date';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid date';
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getActiveCreatorsCount(): number {
    return this.creators.filter(c => c.is_active === true).length;
  }

  // Safe getters for dashboard stats
  getTotalProjects(): number {
    return this.dashboardStats?.projects?.total || 0;
  }

  getActiveProjects(): number {
    return this.dashboardStats?.projects?.active || 0;
  }

  getCompletedProjects(): number {
    return this.assignedProjects.filter(project => 
      project.posts_count > 0 && project.pending_posts === 0
    ).length;
  }

  getTotalPosts(): number {
    return this.dashboardStats?.content?.total_posts || 0;
  }

  getPendingReviews(): number {
    return this.dashboardStats?.content?.pending_reviews || 0;
  }

  getApprovedPosts(): number {
    return this.dashboardStats?.content?.approved_posts || 0;
  }

  getRejectedPosts(): number {
    return this.dashboardStats?.content?.rejected_posts || 0;
  }

  getTotalCreators(): number {
    return this.dashboardStats?.creators?.total_creators || 0;
  }

  showSuccess(message: string) {
    this.snackBar.open(message, 'Close', { duration: 3000 });
  }

  showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Project Management Methods
  getMediaType(url: string): string {
    if (!url) return 'unknown';
    const extension = url.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension || '')) {
      return 'image';
    } else if (['mp4', 'webm', 'ogg'].includes(extension || '')) {
      return 'video';
    } else if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(extension || '')) {
      return 'file';
    } else {
      return 'unknown';
    }
  }

  getFileName(url: string): string {
    if (!url) return 'Unknown File';
    const parts = url.split('/');
    return parts[parts.length - 1];
  }

  isImage(url: string): boolean {
    return this.getMediaType(url) === 'image';
  }

  isVideo(url: string): boolean {
    return this.getMediaType(url) === 'video';
  }

  openCreatorAssignmentDialog(projectId: number) {
    // For now, use a simple multi-select prompt
    // In a real implementation, you'd create a proper dialog component
    const project = this.assignedProjects.find(p => p.id === projectId);
    if (!project) return;

    const availableCreatorNames = this.availableCreators.map(c =>
      `${c.id}: ${c.full_name} (${c.email})`
    ).join('\n');

    const selectedIds = prompt(
      `Available Creators:\n${availableCreatorNames}\n\nEnter creator IDs separated by commas:`
    );

    if (selectedIds) {
      const creatorIds = selectedIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      this.assignCreatorsToProject(projectId, creatorIds);
    }
  }

  assignCreatorsToProject(projectId: number, creatorIds: number[]) {
    this.companyAdminService.assignCreatorsToProject(projectId, { creator_ids: creatorIds }).subscribe({
      next: (response) => {
        this.showSuccess(response.message);
        this.loadDashboardData(); // Refresh data
      },
      error: (error) => {
        console.error('Error assigning creators:', error);
        this.showError('Failed to assign creators to project');
      }
    });
  }
  viewAnalytics() {
    this.router.navigate(['/company-admin/analytics']);
  }

  logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    this.router.navigate(['/login']);
  }

  openBlogReviewDialog(action: 'approve' | 'reject' | 'changes', blog: Blog) {
    const dialogRef = this.dialog.open(BlogReviewDialogComponent, {
      width: '400px',
      data: { action: action, blogId: blog.id, currentComments: blog.review_comments }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.action === 'approve') {
          this.approveBlog(blog.id!, result.comments); // comments might be empty for approve
        } else if (result.action === 'reject') {
          if (result.comments) {
            this.rejectBlog(blog.id!, result.comments);
          } else {
            this.showError('Rejection reason is required.');
          }
        } else if (result.action === 'changes') {
          if (result.comments) {
            this.requestBlogChanges(blog.id!, result.comments);
          } else {
            this.showError('Change instructions are required.');
          }
        }
      }
    });
  }

  isBlog(item: any): item is Blog & {type: string} {
    return item && item.type === 'blog';
  }

  isPost(item: any): item is ContentPost & {type: string} {
    return item && item.type === 'post';
  }

  getSubmittedDate(item: ContentPost | Blog): string {
    if (item.type === 'blog') {
      return item.submitted_at ? this.formatDate(item.submitted_at) : 'N/A';
    } else if (item.type === 'post') {
      return item.scheduled_date ? this.formatDate(item.scheduled_date) : 'N/A';
    }
    return 'N/A';
  }
}
