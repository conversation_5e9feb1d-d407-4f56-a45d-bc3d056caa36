from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone

# Create your models here.
class User(AbstractUser):
    ROLE_CHOICES = (
        ('super_admin', 'Super Admin'),
        ('company_admin', 'Company Admin'),
        ('creator', 'Creator'),
    )
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    company = models.ForeignKey('Company', null=True, blank=True, on_delete=models.SET_NULL, related_name='users')

    def __str__(self):
        return f"{self.username} ({self.role})"

class Company(models.Model):
    name = models.CharField(max_length=255)
    # Remove the confusing 'creator' field - creators are linked via User.company
    logo = models.ImageField(upload_to='company_logos/', null=True, blank=True)

    def __str__(self):
        return self.name

    @property
    def company_admins(self):
        """Get all company admins assigned to this company"""
        return self.users.filter(role='company_admin')

    @property
    def creators(self):
        """Get all creators assigned to this company"""
        return self.users.filter(role='creator')

class Project(models.Model):
    name = models.CharField(max_length=255)
    title = models.CharField(max_length=255, blank=True, null=True, help_text="Project title for display")
    description = models.TextField(blank=True, null=True, help_text="Project description")
    deadline = models.DateTimeField(null=True, blank=False, help_text="Project deadline - Required field")
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='projects')
    company_admin = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_projects', limit_choices_to={'role': 'company_admin'}, help_text="Assigned Company Admin for this project")
    creators = models.ManyToManyField(User, related_name='assigned_projects', limit_choices_to={'role': 'creator'})
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_projects')
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.title or self.name} ({self.company.name})"



class Post(models.Model):
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='posts')
    creator = models.ForeignKey('User', on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    description = models.TextField()
    media = models.FileField(upload_to='posts/', null=True, blank=True)
    media_width = models.PositiveIntegerField(null=True, blank=True)
    media_height = models.PositiveIntegerField(null=True, blank=True)
    scheduled_date = models.DateTimeField(null=True, blank=True)
    scheduled_time = models.DateTimeField(default=timezone.now)
    status = models.CharField(max_length=20, choices=[
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('posted', 'Posted'),
        ('rejected', 'Rejected'),
        ('rework', 'Rework'),
        ('scheduled', 'Scheduled')
    ], default='draft')
    review_comments = models.TextField(blank=True, null=True, help_text="Comments from company admin during review")
    reviewed_by = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_posts', limit_choices_to={'role': 'company_admin'})
    reviewed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


    def __str__(self):
        return f"Post by {self.creator.username} on {self.scheduled_date} - {self.status}"
    
class Blog(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('changes_requested', 'Changes Requested'),
        ('posted', 'Posted'),
        ('scheduled', 'Scheduled')
    ]

    title = models.CharField(max_length=255)
    author_name = models.CharField(max_length=255)  # From input field
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blogs')
    scripture = models.TextField(blank=True)
    content = models.TextField()
    cover_image = models.ImageField(upload_to='blog_covers/', null=True, blank=True)
    media_width = models.PositiveIntegerField(null=True, blank=True)
    media_height = models.PositiveIntegerField(null=True, blank=True)
    tags = models.CharField(max_length=255, blank=True, help_text="Comma-separated tags")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    submitted_at = models.DateTimeField(null=True, blank=True) # New field
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_blogs', limit_choices_to={'role': 'company_admin'})
    reviewed_at = models.DateTimeField(null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, blank=True)  # Optional, if relevant
    review_comments = models.TextField(blank=True, null=True, help_text="Comments from company admin during review")

    def __str__(self):
        return f"{self.title} by {self.author_name}"
