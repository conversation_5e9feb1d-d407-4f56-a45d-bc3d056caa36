export interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  hasEvents: boolean;
  events: CalendarEvent[];
}

export interface CalendarPost {
  id: number;
  title: string;
  description: string;
  time: string;
  status: PostStatus;
  mediaType: 'image' | 'video' | 'file' | null;
  thumbnailUrl?: string;
  scheduledDate: Date;
  projectTitle: string;
  companyName?: string;
  projectId: number;
  companyId: number;
  deadline?: string;
}

export interface CalendarProject {
  id: number;
  title: string;
  name: string;
  description?: string;
  companyName?: string;
  companyId: number;
  deadline?: string;
  created_at?: string;
  postsCount: number;
  pendingPostsCount: number;
  company: {
    id: number;
    name: string;
    logo?: string;
  };
  company_admin?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    full_name: string;
  };
}

export interface CalendarEvent {
  id: number;
  title: string;
  start: Date;
  end?: Date;
  status: PostStatus | BlogStatus;
  type: 'post' | 'blog' | 'project';
  projectId?: number;
  companyId?: number;
  projectTitle?: string;
  companyName?: string;
  author_name?: string;
  creator_name?: string;
  mediaType?: string;
  mediaUrl?: string;
  coverImage?: string;
  content?: string;
  description?: string;
  submitted_at?: string;
  review_comments?: string;
}

export type PostStatus = 'draft' | 'submitted' | 'posted' | 'rejected' | 'rework' | 'scheduled' | 'approved' | 'changes_requested';

export type BlogStatus = 'draft' | 'submitted' | 'posted' | 'rejected' | 'rework' | 'scheduled' | 'approved' | 'changes_requested';

export interface CalendarFilter {
  statuses: (PostStatus | BlogStatus)[];
  searchTerm: string;
  tags: string[];
  projectIds: number[];
  companyIds: number[];
}

export interface StatusConfig {
  color: string;
  icon: string;
  label: string;
}

export interface CalendarViewState {
  currentDate: Date;
  selectedDate: Date | null;
  filter: CalendarFilter;
  viewMode: 'month' | 'week';
}

export interface CreatorDashboardData {
  events: CalendarEvent[];
  projects: CalendarProject[];
}
