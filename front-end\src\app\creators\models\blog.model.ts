export interface Blog {
  id?: number;
  title: string;
  author_name: string;
  author?: number;
  scripture?: string;
  content: string;
  cover_image?: string;
  tags?: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'changes_requested' | 'posted' | 'scheduled';
  submitted_at?: string;
  reviewed_by?: number;
  reviewed_at?: string;
  review_comments?: string;
  project: number;
  project_detail?: {
    id: number;
    name: string;
    title?: string;
    company_detail?: {
      id: number;
      name: string;
    };
  };
  creator_detail?: any;
  reviewed_by_detail?: any;
} 