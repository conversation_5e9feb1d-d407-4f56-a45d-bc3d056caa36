import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, forkJoin } from 'rxjs';
import { map } from 'rxjs/operators';
import { CalendarEvent, CalendarProject, CreatorDashboardData } from '../dashboard/calendar-view/calendar-view.models';
import { Post } from '../models/post.model';
import { Blog } from '../models/blog.model';

@Injectable({
  providedIn: 'root'
})
export class CreatorDashboardService {
  private readonly API_BASE = 'http://127.0.0.1:8000/api/creator-dashboard';
  private readonly BLOG_API_BASE = 'http://127.0.0.1:8000/api/blogs'; // Added Blog API base
  
  // State management
  private assignedProjectsSubject = new BehaviorSubject<CalendarProject[]>([]);
  private calendarEventsSubject = new BehaviorSubject<CalendarEvent[]>([]);
  
  public assignedProjects$ = this.assignedProjectsSubject.asObservable();
  public calendarEvents$ = this.calendarEventsSubject.asObservable();

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  private getAuthHeadersForFormData(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData, let browser set it with boundary
    });
  }

  /**
   * Get projects assigned to the logged-in creator
   */
  getAssignedProjects(): Observable<CalendarProject[]> {
    return this.http.get<CalendarProject[]>(
      `${this.API_BASE}/my_projects/`,
      { headers: this.getAuthHeaders() }
    ).pipe(
      map(projects => {
        this.assignedProjectsSubject.next(projects);
        return projects;
      })
    );
  }

  /**
   * Get posts created by the creator (filtered by assigned projects)
   * This method might become redundant if getCalendarData fetches all events.
   * Keeping it for now as it's used elsewhere.
   */
  getMyPosts(): Observable<Post[]> {
    return this.http.get<Post[]>(
      `${this.API_BASE}/my_posts/`,
      { headers: this.getAuthHeaders() }
    );
  }

  /**
   * Get blogs created by the creator
   * This method will remain as it's used in blog-list.component.ts
   */
  getMyBlogs(): Observable<Blog[]> {
    return this.http.get<Blog[]>(
      `${this.API_BASE}/my_blogs/`,
      { headers: this.getAuthHeaders() }
    );
  }

  /**
   * Get unified calendar data with project assignments, posts, and blogs.
   */
  getCalendarData(): Observable<CreatorDashboardData> {
    const projects$ = this.getAssignedProjects();
    const posts$ = this.getMyPosts(); // Re-using existing method for posts
    const blogs$ = this.getMyBlogs(); // Re-using existing method for blogs

    return forkJoin([projects$, posts$, blogs$]).pipe(
      map(([projects, posts, blogs]) => {
        // Map posts to CalendarEvent format
        const postEvents: CalendarEvent[] = posts.map(post => ({
          id: post.id || 0,
          type: 'post',
          title: post.title,
          description: post.description || '',
          start: new Date(post.scheduled_date || ''),
          status: post.status,
          projectId: post.project,
          companyId: post.project_detail?.company_detail?.id,
          projectTitle: post.project_detail?.title || post.project_detail?.name,
          companyName: post.project_detail?.company_detail?.name ?? '',
          mediaType: post.mediaType || undefined,
          mediaUrl: post.media_url,
          content: post.description, // Assuming post description can be content for preview
          submitted_at: post.created_at, // Use created_at for submitted_at for posts
          review_comments: post.review_comments,
          reviewed_by_detail: post.reviewed_by_detail,
          reviewed_at: post.reviewed_at,
        }));

        // Map blogs to CalendarEvent format
        const blogEvents: CalendarEvent[] = blogs.map(blog => ({
          id: blog.id || 0,
          type: 'blog',
          title: blog.title,
          description: blog.content || '',
          start: new Date(blog.submitted_at || ''),
          status: blog.status,
          projectId: blog.project,
          companyId: blog.project_detail?.company_detail?.id,
          projectTitle: blog.project_detail?.title || blog.project_detail?.name,
          companyName: blog.project_detail?.company_detail?.name ?? '',
          mediaUrl: blog.cover_image,
          mediaType: blog.cover_image ? 'image' : undefined,
          content: blog.content,
          coverImage: blog.cover_image,
          submitted_at: blog.submitted_at,
          review_comments: blog.review_comments,
          reviewed_by_detail: blog.reviewed_by_detail,
          reviewed_at: blog.reviewed_at,
        }));

        const allEvents = [...postEvents, ...blogEvents];

        this.calendarEventsSubject.next(allEvents);
        this.assignedProjectsSubject.next(projects);

        return { events: allEvents, projects: projects };
      })
    );
  }

  /**
   * Create a new post
   */
  createPost(postData: FormData): Observable<Post> {
    return this.http.post<Post>(
      `${this.API_BASE}/upload_post/`,
      postData,
      { headers: this.getAuthHeadersForFormData() }
    );
  }

  /**
   * Get posts for a specific date (now includes blogs)
   */
  getEventsForDate(date: Date): Observable<CalendarEvent[]> {
    const dateStr = date.toISOString().split('T')[0];
    return this.calendarEvents$.pipe(
      map(events => events.filter(event => 
        event.start.toISOString().startsWith(dateStr) && (event.type === 'post' || event.type === 'blog')
      ))
    );
  }

  /**
   * Get projects for a specific date (deadlines)
   */
  getProjectsForDate(date: Date): Observable<CalendarEvent[]> {
    const dateStr = date.toISOString().split('T')[0];
    return this.calendarEvents$.pipe(
      map(events => events.filter(event => 
        event.end?.toISOString().startsWith(dateStr) && event.type === 'project'
      ))
    );
  }

  /**
   * Check if creator has access to a specific project
   */
  hasProjectAccess(projectId: number): Observable<boolean> {
    return this.assignedProjects$.pipe(
      map(projects => projects.some(project => project.id === projectId))
    );
  }

  /**
   * Get project details by ID (only if assigned)
   */
  getProjectById(projectId: number): Observable<CalendarProject | null> {
    return this.assignedProjects$.pipe(
      map(projects => projects.find(project => project.id === projectId) || null)
    );
  }

  /**
   * Filter events by project
   */
  filterEventsByProject(projectId: number): Observable<CalendarEvent[]> {
    return this.calendarEvents$.pipe(
      map(events => events.filter(event => event.projectId === projectId))
    );
  }

  /**
   * Filter events by company
   */
  filterEventsByCompany(companyId: number): Observable<CalendarEvent[]> {
    return this.calendarEvents$.pipe(
      map(events => events.filter(event => event.companyId === companyId))
    );
  }

  /**
   * Get events for calendar view (posts, blogs and project deadlines)
   * This method simply returns the subject, as the data is already combined in getCalendarData
   */
  getEventsForCalendar(): Observable<CalendarEvent[]> {
    return this.calendarEvents$;
  }

  /**
   * Refresh all dashboard data
   */
  refreshDashboardData(): Observable<CreatorDashboardData> {
    return this.getCalendarData();
  }

  /**
   * Clear cached data (for logout)
   */
  clearCache(): void {
    this.assignedProjectsSubject.next([]);
    this.calendarEventsSubject.next([]);
  }

  /**
   * Create a new blog
   */
  createBlog(blogData: any): Observable<Blog> {
    // If FormData, do not set Content-Type
    if (blogData instanceof FormData) {
      return this.http.post<Blog>(
        `${this.BLOG_API_BASE}/`,
        blogData,
        { headers: this.getAuthHeadersForFormData() }
      );
    } else {
      return this.http.post<Blog>(
        `${this.BLOG_API_BASE}/`,
        blogData,
        { headers: this.getAuthHeaders() }
      );
    }
  }
}
